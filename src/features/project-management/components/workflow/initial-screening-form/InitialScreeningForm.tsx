'use client';

import type { infosQA, StepInfosQA,
  // StepInfosQA
} from '@/features/project-management/types/step';
import type { IFileResponse, stateRouteAgent } from '@/shared/types/global';
import { useGetInfoDetail, useProjectDetail } from '@/features/project-management/hooks';
import TextArea from '@/shared/components/form/input/TextArea';
import Label from '@/shared/components/form/Label';
import { useCoAgent, useCopilotChat } from '@copilotkit/react-core';
import { Role, TextMessage } from '@copilotkit/runtime-client-gql';
import { zodResolver } from '@hookform/resolvers/zod';
import React, {
  useEffect,

  // useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Controller, useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import FileUpload from './FileUpload';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import {
  useCurrentStep,
  useCurrentTask,
  // useCurrentStep,
  useWorkflowActions,
} from '@/features/project-management/stores/project-workflow-store';
import { useChatBoxShow } from '@/features/project-management/stores/chatbox-store';
import { createDynamicSchema, createPromptData, generateDefaultValues, hasDataChanged,
  // hasDataChanged
} from '@/features/project-management/utils/initialScreeningUtils';
import type { DynamicFormData } from '@/features/project-management/utils/initialScreeningUtils';
import { compareObject } from '@/shared/utils/compareObject';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { useParams } from 'next/navigation';
import type { documentUrlCrew, questionResponseCrew } from '@/features/project-management/types/project';
import type { summarizeFlow } from '@/features/project-management/types/agent';
import { AGENT_NAME_COPILOTKIT, AGENT_ROUTE_NAME, MESSAGE_SEND_ROUTE_AGENT } from '@/shared/constants/global';
import { ENameStateAgentCopilotkit } from '@/shared/enums/global';
import { Button } from '@/shared/components/ui/button';

const InitialScreeningForm: React.FC = () => {
  // State management
  const [files, setFiles] = useState<IFileResponse[]>([]);

  const [isEditing, setIsEditing] = useState(true);

  // Refs for preventing unnecessary re-renders and data processing
  const formResetTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastProcessedDataRef = useRef<string>('');
  const [currentDataStep, setCurrentDataStep] = useState<DynamicFormData>({});

  // Store hooks
  const currentStep = useCurrentStep();
  const currentTask = useCurrentTask();

  // Memoize stable references to prevent unnecessary re-renders
  const currentStepId = currentStep?.id;
  const currentStepData = currentStep?.data;
  const currentStepQAList = currentStep?.qaList;
  // const currentStepStatus = currentStep?.status;

  // const workflow = useWorkflowTasks();

  const params = useParams<{ id: string }>();

  // Store action functions - using individual hooks for stable references
  const {
    updateStepData,
    completeStep,
    updateQAListData,
    setProjectName,
    // updateStatus,
    updateCurrentStepInfoIds,
    getNextStepId,
  } = useWorkflowActions();

  const show = useChatBoxShow();

  const { mutateAsync } = useUpdateStatusStep();

  // API hooks
  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { data: qaStep, isLoading: isQALoading, error: _qaError } = useGetInfoDetail<infosQA, null>(currentStep?.id ?? '');

  const { data: project } = useProjectDetail(params.id);
  // AI agent hooks
  const { appendMessage } = useCopilotChat();
  const { state: coAgentState, setState: setCoAgentsState } = useCoAgent<stateRouteAgent<summarizeFlow>>({
    name: AGENT_ROUTE_NAME,
    initialState: {
      agent_name: AGENT_NAME_COPILOTKIT.SUMMARIZE,
    },
  });

  // Compute loading state                   based on React Query state
  const isDataLoading = isQALoading || (!qaStep && !!currentStep?.id);

  // Generate dynamic schema based on current step qaList
  const dynamicSchema = useMemo(() => {
    if (!currentStepQAList) {
      return z.object({});
    }
    return createDynamicSchema(currentStepQAList);
  }, [currentStepQAList]);

  // Generate default values based on current step data
  const formDefaultValues = useMemo(() => {
    if (!currentStepQAList) {
      return {};
    }
    return generateDefaultValues(currentStepQAList, currentStepData);
  }, [currentStepId]);

  // Initialize React Hook Form with dynamic schema
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<DynamicFormData>({
    resolver: zodResolver(dynamicSchema),
    defaultValues: formDefaultValues,
    mode: 'onSubmit',
  });

  useEffect(() => {
    setCoAgentsState({
      ...coAgentState,
      [ENameStateAgentCopilotkit.SUMMARIZE]: {
        ...(project ? { project_info: { ...project } } : {}),
      },
    });

    setProjectName(project?.name ?? '');
  }, [project]);

  // Memoize the processed QA data to prevent unnecessary recalculations
  const processedQAData = useMemo(() => {
    if (!qaStep) {
      return null;
    }

    const infos = qaStep.stepInfo[qaStep.stepInfo.length - 1]?.infos ?? [];
    const stepInfo = infos.slice(0, -1);
    const filesResponse = infos[infos.length - 1];
    const qaList: StepInfosQA[] = stepInfo ?? [];
    const newQaList = qaList.map(qa => ({
      ...qa,
      name: qa.question.replace(/[^a-z0-9\s]/gi, '').replace(/\s+/g, '_').toLowerCase(),
    }));

    const data = newQaList.reduce((acc, i) => {
      acc[i.name] = i.answer as string;
      return acc;
    }, {} as Record<string, string>);

    setCurrentDataStep(data);
    if (currentStep?.status === EStatusTask.COMPLETED) {
      setIsEditing(false);
    }
    return {
      qaList: newQaList,
      data,
    };
  }, [qaStep]);

  // Consolidated Data Processing & Store Updates
  useEffect(() => {
    if (!processedQAData || !currentStepId) {
      return;
    }

    const { qaList, data } = processedQAData;

    // Create a unique key for this data to prevent duplicate processing
    const dataKey = `${currentStepId}-${JSON.stringify(data)}-${qaList.length}`;
    if (lastProcessedDataRef.current === dataKey) {
      return;
    }

    // Check if the data has actually changed to prevent infinite loops
    const hasStepDataChanged = hasDataChanged(currentStepData, data);
    const hasQAListChanged = hasDataChanged(currentStepQAList, qaList);

    // Only update if the data has actually changed
    if (hasStepDataChanged || hasQAListChanged) {
      // Update store data
      updateStepData(currentStepId, data);
      updateQAListData(currentStepId, qaList);

      // Update step info IDs if available
      if (qaStep?.stepInfo?.[0]?.id) {
        updateCurrentStepInfoIds(qaStep.stepInfo[0].id);
      }

      // Mark this data as processed
      lastProcessedDataRef.current = dataKey;
    }
  }, [processedQAData, currentStepId, currentStepData, currentStepQAList, qaStep]);

  // Consolidated Form Management
  useEffect(() => {
    if (!processedQAData && !currentStepData) {
      return;
    }

    // Clear any existing timeout to prevent multiple resets
    if (formResetTimeoutRef.current) {
      clearTimeout(formResetTimeoutRef.current);
    }

    // Debounce form reset to prevent excessive calls
    formResetTimeoutRef.current = setTimeout(() => {
      const newDefaultValues: DynamicFormData = {};

      // Use processedQAData if available, otherwise use currentStepQAList
      const qaListToUse = processedQAData?.qaList || currentStepQAList || [];

      qaListToUse.forEach((qa) => {
        newDefaultValues[qa.name] = currentStepData?.[qa.name] || '';
      });

      reset(newDefaultValues);

      // Handle files separately if they exist in currentStepData
      if (currentStepData?.files && Array.isArray(currentStepData.files)) {
        setFiles(currentStepData.files);
      }
    }, 50); // Small delay to batch updates

    return () => {
      if (formResetTimeoutRef.current) {
        clearTimeout(formResetTimeoutRef.current);
      }
    };
  }, [processedQAData, currentStepData, currentStepQAList, reset]);

  // Memoized event handlers to prevent unnecessary re-renders
  const handleCreatePrompt = React.useCallback((data: DynamicFormData, files: IFileResponse[]) => {
    return createPromptData(data, files);
  }, []);

  const handleSendPrompt = React.useCallback((questions: questionResponseCrew[], document_url: documentUrlCrew[]) => {
    setCoAgentsState((prevState: any) => ({
      ...prevState,
      [ENameStateAgentCopilotkit.SUMMARIZE]: {
        ...prevState[ENameStateAgentCopilotkit.SUMMARIZE],
        initial_info: [...questions],
        document_url: [...document_url],
      },
    }));

    appendMessage(
      new TextMessage({
        content: MESSAGE_SEND_ROUTE_AGENT,
        role: Role.Developer,
      }),
    );
  }, [setCoAgentsState, appendMessage]); // Removed coAgentState to prevent re-renders

  const handleFilesChange = React.useCallback((uploadedFiles: IFileResponse[]) => {
    setFiles(uploadedFiles);
  }, []);

  const toggleViewMode = () => {
    setIsEditing(prev => !prev);
  };

  useEffect(() => {
    const handleBeforeUnload = (event: any) => {
      event.preventDefault();
      event.returnValue = ''; // Required for some browsers
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Cleanup: Remove the event listener on unmount
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);
  // Memoized form submission handler with stable dependencies
  const onSubmit = React.useCallback(async (data: DynamicFormData) => {
    if (!currentStepId) {
      return;
    }

    const isChanged = !compareObject(data, currentDataStep);

    // Combine form data with files
    const formData = {
      ...data,
      files,
    };
    const prompt = handleCreatePrompt(data, files);
    const { questions, files: fileResponse } = prompt;
    const setQuestion: Map<string, string> = new Map();

    currentStepQAList?.forEach((qa) => {
      setQuestion.set(qa.name, qa.question);
    });

    const infos = questions.map((q, index) => ({
      question: setQuestion.get((q.questions) ?? '') ?? '',
      answer: q.answer ?? '',
      id: `${index + 1}`,
    }));

    if (infos.some(quiz => !quiz.question.length)) {
      console.log('some of the question is empty due to unexpected error');
      return;
    }

    await updateQuestionAnswer({
      stepInfos: [{
        order: 0,
        infos: [...infos, {
          url: files.map(file => ({
            file: file.key,
            name: file.originalname,
            type: file.mimeType,
            id: file._id,
          })),
        }],
      }],
      // order: 0,
      // infos: dataInfos.infos,
    }, currentStepId);
    if (currentStep.status !== EStatusTask.COMPLETED || (isChanged && currentStep.status === EStatusTask.COMPLETED)) {
      handleSendPrompt(infos, fileResponse);
      show();

      // Reset data in DB
      const id = getNextStepId();
      updateQuestionAnswer({ stepInfos: [] }, id ?? '');

      // Reset scoring data in next step
      // updateScoreDetail({ stepInfo: [] } as any);
      mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.IN_PROGRESS });
    }
    updateStepData(currentStepId, formData);
    mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });

    completeStep(currentStepId);
    toast.success('Initial screening form processed successfully', {
      duration: 3000,
    });
  }, [currentStepId, files, handleCreatePrompt, currentStepQAList, updateQuestionAnswer, handleSendPrompt, show, updateStepData, completeStep]);

  // Cleanup function for component unmount
  React.useEffect(() => {
    return () => {
      if (formResetTimeoutRef.current) {
        clearTimeout(formResetTimeoutRef.current);
      }
    };
  }, []);

  // Show loading if we don't have currentStep yet
  if (!currentStep) {
    return (
      <div className="relative">
        <div className="space-y-6 p-4 md:p-6">
          <ProjectCardSkeleton />
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 p-4 md:p-6">
        {/* Dynamically render form fields based on qaList */}
        {isDataLoading
          ? (
              <ProjectCardSkeleton />
            )
          : (
              currentStepQAList?.map(qa => (
                <div key={qa.id}>
                  <Label htmlFor={qa.name} className="mb-1.5 block text-primary">
                    {qa.question}
                  </Label>
                  <Controller
                    name={qa.name}
                    control={control}
                    render={({ field }) => (
                      <TextArea
                        placeholder="Enter your answer"
                        rows={3}
                        value={field.value}
                        onChange={field.onChange}
                        error={!!errors[qa.name]}
                        hint={errors[qa.name]?.message}
                        disabled={!isEditing}
                      />
                    )}
                  />
                </div>
              ))
            )}

        {/* File Upload */}
        <div>
          <Label htmlFor="files" className="mb-1.5 block text-primary">
            Attached Files
          </Label>
          <FileUpload onFilesChange={handleFilesChange} />
        </div>
      </form>

      <WorkflowNavigation
        onComplete={handleSubmit(onSubmit)}
        disableNext={isSubmitting}
        showPrevious={false}
      >
        { currentStep && currentStep.status === EStatusTask.COMPLETED && (
          <Button
            type="button"
            className={`bg-warning-400 text-white rounded-lg hover:bg-warning-500 ${isEditing ? 'bg-error-500 hover:bg-error-600' : ''}`}
            onClick={toggleViewMode}
            disabled={isSubmitting}
          >
            {isEditing ? 'Cancel' : 'Edit'}
          </Button>
        )}
      </WorkflowNavigation>
    </div>
  );
};

export default InitialScreeningForm;
