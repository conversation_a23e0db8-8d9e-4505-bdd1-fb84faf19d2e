'use client';

import Label from '@/shared/components/form/Label';
import Radio from '@/shared/components/form/input/Radio';
import FileUpload from '../initial-screening-form/FileUpload';
import React, { useEffect, useState } from 'react';
import type { IFileResponse, stateRouteAgent } from '@/shared/types/global';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import { useClientFileUploaded, useCurrentStep, useCurrentTask, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import { useCoAgent, useCopilotChat } from '@copilotkit/react-core';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { BriefAnalysisFlow } from '@/features/project-management/types/agent';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import { Role, TextMessage } from 'node_modules/@copilotkit/runtime-client-gql/dist/client/types';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import type { fileUploadResponse, stepInfoFile } from '@/features/project-management/types/project';
import { Env } from '@/core/config/Env';
import { AGENT_NAME_COPILOTKIT, AGENT_ROUTE_NAME, MESSAGE_SEND_ROUTE_AGENT } from '@/shared/constants/global';
import { ENameStateAgentCopilotkit } from '@/shared/enums/global';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { compareObjectArray } from '@/shared/utils/compareObject';

// Service options data - moved outside component to avoid re-renders
const SERVICE_OPTIONS = [
  { id: 'corporate', label: 'Corporate' },
  { id: 'crisis-management', label: 'Crisis Management' },
  { id: 'event', label: 'Event' },
  { id: 'gr-advocacy', label: 'GR-Advocacy' },
  { id: 'imc', label: 'IMC' },
  { id: 'market-research', label: 'Market Research' },
  { id: 'media-relation-pr', label: 'Media Relation - PR' },
  { id: 'mibrand-branding', label: 'Mibrand Branding' },
  { id: 'product-launch', label: 'Product Launch' },
  { id: 'social-digital-corporate', label: 'Social & Digital Corporate' },
  { id: 'social-media-digital-product', label: 'Social Media & Digital Product' },
  { id: 'tvc-video-production', label: 'TVC/Video Production' },
];

const ClientUploadForm: React.FC = () => {
  const [_files, setFiles] = useState<IFileResponse[]>([]);

  const [initialFile, setInitialFile] = useState<IFileResponse[]>([]);

  const [selectedOption, setSelectedOption] = useState<string>('imc'); // Default value

  // Custom Hook

  const currentStep = useCurrentStep();

  const currentTask = useCurrentTask();

  const currentStepId = currentStep?.id;

  const clientFileUploaded = useClientFileUploaded();

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { setClientFileUploaded, getNextStepId } = useWorkflowActions();

  const { mutateAsync } = useUpdateStatusStep();

  const { data: fileUpload } = useGetInfoDetail<fileUploadResponse, any>(currentStep?.id ?? '');

  //  Set Agent

  const { setState: setCoAgentsState } = useCoAgent<stateRouteAgent<BriefAnalysisFlow>>({
    name: AGENT_ROUTE_NAME,
  });

  const { appendMessage } = useCopilotChat();

  const saveInitialFile = (file: IFileResponse[]) => {
    const setInitialFileStore = () => {
      setInitialFile(file);
      setFiles(file);
    };

    setInitialFileStore();
  };

  const updateInitialFile = () => {
    const setFile = () => {
      const files = ((fileUpload?.stepInfo[0]?.infos ?? []).map(
        (info: stepInfoFile) => ({
          mimeType: info.type,
          originalname: info.name,
          key: info.file,
          filename: info.name,
          url: `${Env.NEXT_PUBLIC_API_SERVER}/public/${info.file}`,
          _id: info.id,
        }),
      ));
      saveInitialFile(files);
      setClientFileUploaded(files);
    };

    setFile();
  };

  useEffect(() => {
    saveInitialFile(clientFileUploaded);
    if (fileUpload && fileUpload.stepInfo.length) {
      updateInitialFile();
    }
  }, [fileUpload]);

  // Separate effect to handle service option from API
  useEffect(() => {
    if (fileUpload && fileUpload.stepInfo.length) {
      // Check if there's a saved service option from API
      // Assuming the service option might be stored in a custom field or metadata
      const stepInfo = fileUpload.stepInfo[0];
      const savedServiceOption = (stepInfo as any)?.serviceOption || (stepInfo as any)?.metadata?.serviceOption;

      if (savedServiceOption && SERVICE_OPTIONS.some(option => option.id === savedServiceOption)) {
        setSelectedOption(savedServiceOption);
      }
    }
  }, [fileUpload]);

  const {
    completeStep,
  } = useWorkflowActions();

  const handleFilesChange = React.useCallback((uploadedFiles: IFileResponse[]) => {
    setFiles(uploadedFiles);
  }, []);

  const handleOptionSelect = (optionId: string) => {
    setSelectedOption(optionId);
  };

  const handleSendMessage = () => {
    setCoAgentsState((prevState: any) => ({
      ...prevState,
      agent_name: AGENT_NAME_COPILOTKIT.ANSWER,
      [ENameStateAgentCopilotkit.ANALYSIS]: {
        ...prevState[ENameStateAgentCopilotkit.ANALYSIS],
        client_brief_url: getFile(_files),
      },
    }));

    appendMessage(
      new TextMessage({
        content: MESSAGE_SEND_ROUTE_AGENT,
        role: Role.Developer,
      }),
    );
  };

  const onSubmit = async () => {
    if (!currentStepId) {
      return;
    }
    const payload = {
      stepInfos: [
        {
          order: 0,
          infos: _files.map(file => ({
            file: file.key,
            name: file.originalname,
            type: file.mimeType,
            id: file._id,
          })),
          serviceOption: selectedOption,
        },
      ],
    };

    const isChanged = !compareObjectArray(initialFile, _files);
    const nextStepId = getNextStepId();
    await updateQuestionAnswer(
      payload,
      currentStepId,
    );

    if (currentStep.status !== EStatusTask.COMPLETED || (isChanged && currentStep.status === EStatusTask.COMPLETED)) {
      handleSendMessage();
      updateQuestionAnswer({ stepInfos: [] }, nextStepId);
    }
    if (currentStep.status !== EStatusTask.COMPLETED) {
      mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
    }
    if (currentTask && (currentTask.status === EStatusTask.PENDING)) {
      mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.IN_PROGRESS });
    }

    completeStep(currentStepId);
  };

  return (
    <div className="relative">
      <div className="space-y-6 p-4 md:p-6">
        {/* Service Options Grid */}
        <div className="mb-6">
          <div className="grid grid-cols-3 gap-4">
            {SERVICE_OPTIONS.map(option => (
              <Radio
                key={option.id}
                id={`service-${option.id}`}
                name="service-options"
                value={option.id}
                checked={selectedOption === option.id}
                onChange={handleOptionSelect}
                label={option.label}
              />
            ))}
          </div>
        </div>

        <Label htmlFor="files" className="mb-1.5 block text-primary">
          Attached Files
        </Label>
        <FileUpload initialFile={initialFile} onFilesChange={handleFilesChange} />
      </div>

      <WorkflowNavigation
        onComplete={onSubmit}
        nextButtonText="Generate"
        showPrevious={false}
      />
    </div>
  );
};

export default ClientUploadForm;
