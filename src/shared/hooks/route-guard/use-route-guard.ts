'use client';

import { useEffect, useRef } from 'react';
import { useRouter } from 'next/router';

type UseRouteGuardOptions = {
  when: boolean;
  message?: string;
  onBeforeUnload?: () => void;
  onRouteChangeStart?: () => boolean;
};

export function useRouteGuard({
  when,
  message = 'You have unsaved changes. Are you sure you want to leave?',
  onBeforeUnload,
  onRouteChangeStart,
}: UseRouteGuardOptions) {
  const router = useRouter();
  const savedCallback = useRef<() => boolean>(() => true);

  // Save the latest callback
  useEffect(() => {
    savedCallback.current = onRouteChangeStart ?? (() => true);
  }, [onRouteChangeStart]);

  // Handle browser navigation (refresh, close tab, etc.)
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (when) {
        onBeforeUnload?.();
        e.preventDefault();
        e.returnValue = message;
        return message;
      }
    };

    if (when) {
      window.addEventListener('beforeunload', handleBeforeUnload);
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [when, message, onBeforeUnload]);

  // Handle Next.js router navigation
  useEffect(() => {
    const handleRouteChangeStart = (url: string) => {
      if (when && router.asPath !== url) {
        const shouldProceed = savedCallback.current?.() ?? window.confirm(message);
        if (!shouldProceed) {
          router.events.emit('routeChangeError');
          throw new Error('Route change aborted.');
        }
      }
    };

    if (when) {
      router.events.on('routeChangeStart', handleRouteChangeStart);
    }

    return () => {
      router.events.off('routeChangeStart', handleRouteChangeStart);
    };
  }, [when, message, router]);
}
