'use client';

import { useCallback, useState } from 'react';
import { useRouter } from 'next/router';
import { useRouteGuard } from './use-route-guard';

type UseRouteGuardWithDialogOptions = {
  when: boolean;
  title?: string;
  message?: string;
};

export function useRouteGuardWithDialog({
  when,
  title = 'Unsaved Changes',
  message = 'You have unsaved changes. Are you sure you want to leave?',
}: UseRouteGuardWithDialogOptions) {
  const [showDialog, setShowDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(null);
  const router = useRouter();

  const handleRouteChangeStart = useCallback(() => {
    if (when) {
      setShowDialog(true);
      return false; // Block navigation initially
    }
    return true;
  }, [when]);

  const handleConfirm = useCallback(() => {
    setShowDialog(false);
    if (pendingNavigation) {
      // Temporarily disable the guard and navigate
      router.push(pendingNavigation);
      setPendingNavigation(null);
    }
  }, [pendingNavigation, router]);

  const handleCancel = useCallback(() => {
    setShowDialog(false);
    setPendingNavigation(null);
  }, []);

  useRouteGuard({
    when,
    message,
    onRouteChangeStart: handleRouteChangeStart,
  });

  return {
    showDialog,
    title,
    message,
    onConfirm: handleConfirm,
    onCancel: handleCancel,
  };
}
